from __future__ import annotations
from typing import List, Dict, Any
import utils
import ai_config
import shell_utils
import editor
import llm

def repl(allow_self_edit: bool = False) -> None:
    history = utils.load_history()
    print("AI Terminal Agent — type 'help' for commands. Self-edit allowed:", allow_self_edit)
    while True:
        try:
            raw = input("agent> ").strip()
        except (KeyboardInterrupt, EOFError):
            print("\nExiting.")
            utils.save_history(history)
            break
        if not raw:
            continue
        utils.log(history, "user", raw)
        cmd, *rest = raw.split(maxsplit=1)
        tail = rest[0] if rest else ""
        if cmd in {"exit", "quit"}:
            print("Goodbye.")
            utils.save_history(history)
            break
        elif cmd == "help":
            print("""Commands:
  help                      Show this help
  run <cmd>                 Run shell command (dangerous commands require CONFIRM)
  think <instruction>       Ask LLM (if configured) or local heuristic
  edit <path>               Edit a file (uses $EDITOR or tiny built-in editor)
  self-edit                 Edit this agent's source file (requires explicit confirmation)
  history                   Show recent history entries
  save-history              Save history immediately
  config                    Show current LLM configuration
  exit / quit               Exit
Notes:
  - To enable LLM: set GROQ_API_KEY for Groq or LOCAL_LLM_URL for local models (LM Studio).
  - Install 'requests' package for LLM functionality.
  - Destructive shell commands require typing CONFIRM.
""")
        elif cmd == "run":
            if not tail:
                print("Usage: run <shell command>")
                continue
            if shell_utils.is_dangerous_shell(tail):
                print("Dangerous command detected.")
                if not utils.confirm("You are about to run a dangerous shell command."):
                    print("Canceled.")
                    continue
            print(f"Running: {tail}")
            rc = shell_utils.run_shell(tail)
            utils.log(history, "system", f"shell return {rc}")
        elif cmd == "think":
            if not tail:
                print("Usage: think <instruction>")
                continue
            print("Thinking...")
            out = llm.llm_think(tail, history)
            print(out)
            utils.log(history, "assistant", out)
        elif cmd == "edit":
            if not tail:
                print("Usage: edit <path)")
                continue
            ok = editor.edit_file(tail)
            if ok:
                utils.log(history, "system", f"edited {tail}")
        elif cmd == "self-edit":
            if not allow_self_edit:
                print("Self-editing is sensitive.")
                if not utils.confirm("To edit the agent source, type SELF-EDIT:"):
                    print("Self edit aborted.")
                    continue
                token = input("Type SELF-EDIT to confirm: ").strip()
                if token != "SELF-EDIT":
                    print("Wrong token. Aborting.")
                    continue
            ok = editor.edit_file(ai_config.SELF_PATH)
            if ok:
                utils.log(history, "system", "self-modified source")
                print("Agent source edited. You may want to restart the script to load changes.")
        elif cmd == "history":
            for i, item in enumerate(history[-40:], start=1):
                ts = item.get("ts", "?")
                role = item.get("role", "?")
                text = item.get("text", "").replace("\n", " ")
                print(f"{i:3d}. [{ts}] {role}: {text[:200]}")
        elif cmd == "save-history":
            utils.save_history(history)
            print("Saved.")
        elif cmd == "set":
            # Usage: set VAR VALUE
            if not tail:
                print("Usage: set <VAR> <VALUE>  (e.g. set LOCAL_LLM_URL http://127.0.0.1:1234/v1/chat/completions)")
                continue
            parts = tail.split(maxsplit=1)
            key = parts[0]
            val = parts[1] if len(parts) > 1 else ""
            allowed = {"GROQ_HOST_IP", "GROQ_API_URL", "GROQ_INSECURE", "GROQ_API_KEY", "GROQ_MODEL",
                      "LOCAL_LLM_URL", "LOCAL_LLM_MODEL", "LOCAL_LLM_API_KEY"}
            if key not in allowed:
                print("Allowed keys:", ", ".join(sorted(allowed)))
                continue
            if key == "GROQ_INSECURE":
                setattr(ai_config, key, val.lower() in ("1", "true", "yes"))
            else:
                setattr(ai_config, key, val)
            print(f"Set {key}.")
            # update LLM availability heuristics if relevant keys changed
            if key in {"GROQ_API_KEY", "LOCAL_LLM_URL"}:
                try:
                    import requests  # type: ignore
                    ai_config.GROQ_AVAILABLE = bool(ai_config.GROQ_API_KEY and requests)
                    ai_config.LLM_AVAILABLE = bool(requests and (ai_config.GROQ_AVAILABLE or ai_config.LOCAL_LLM_URL))
                except Exception:
                    ai_config.GROQ_AVAILABLE = False
                    ai_config.LLM_AVAILABLE = False
        elif cmd in {"show-config", "config"}:
            # show current LLM configuration (mask API keys)
            def mask(s: str) -> str:
                if not s:
                    return "<unset>"
                if len(s) <= 8:
                    return "<set>"
                return s[:4] + "..." + s[-4:]

            print("=== LLM Configuration ===")
            print("LLM_AVAILABLE:", ai_config.LLM_AVAILABLE)
            print()
            print("--- Groq Settings ---")
            print("GROQ_AVAILABLE:", ai_config.GROQ_AVAILABLE)
            print("GROQ_API_KEY:", mask(ai_config.GROQ_API_KEY))
            print("GROQ_MODEL:", ai_config.GROQ_MODEL)
            print("GROQ_API_URL:", ai_config.GROQ_API_URL)
            print("GROQ_HOST_IP:", ai_config.GROQ_HOST_IP or "<unset>")
            print("GROQ_INSECURE:", ai_config.GROQ_INSECURE)
            print()
            print("--- Local LLM Settings (LM Studio) ---")
            print("LOCAL_LLM_URL:", ai_config.LOCAL_LLM_URL)
            print("LOCAL_LLM_MODEL:", ai_config.LOCAL_LLM_MODEL)
            print("LOCAL_LLM_API_KEY:", mask(ai_config.LOCAL_LLM_API_KEY))
        else:
            if raw.startswith("!"):
                shell_cmd = raw[1:].strip()
                if shell_utils.is_dangerous_shell(shell_cmd):
                    print("Dangerous command detected.")
                    if not utils.confirm("Confirm dangerous shell execution:"):
                        continue
                rc = shell_utils.run_shell(shell_cmd)
                utils.log(history, "system", f"shell return {rc}")
            else:
                print("No command matched. Sending to think() as default.")
                out = llm.llm_think(raw, history)
                print(out)
                utils.log(history, "assistant", out)
        if len(history) % 10 == 0:
            try:
                utils.save_history(history)
            except Exception:
                pass

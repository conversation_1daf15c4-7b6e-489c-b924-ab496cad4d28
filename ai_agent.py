# path: ai_agent.py
from __future__ import annotations
import sys
import ai_config
from repl import repl


def main() -> None:
    allow_self_edit = "--allow-self-edit" in sys.argv
    if "--offline" in sys.argv:
        ai_config.FORCE_OFFLINE = True
    try:
        import requests  # type: ignore
        # Check for Groq availability (legacy)
        ai_config.GROQ_AVAILABLE = bool(ai_config.GROQ_API_KEY and requests)
        # Check for local LLM availability (LM Studio)
        ai_config.LLM_AVAILABLE = bool(requests and (ai_config.GROQ_AVAILABLE or ai_config.LOCAL_LLM_URL))
    except Exception:
        ai_config.GROQ_AVAILABLE = False
        ai_config.LLM_AVAILABLE = False

    if ai_config.LLM_AVAILABLE:
        if ai_config.GROQ_AVAILABLE:
            print("Starting agent. LLM: Groq")
        else:
            print(f"Starting agent. LLM: Local ({ai_config.LOCAL_LLM_MODEL})")
    else:
        print("Starting agent. LLM: None (local mode)")
    repl(allow_self_edit=allow_self_edit)


if __name__ == "__main__":
    main()

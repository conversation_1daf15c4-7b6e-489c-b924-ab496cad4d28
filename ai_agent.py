# path: ai_agent.py
from __future__ import annotations
import sys
import ai_config
from repl import repl


def main() -> None:
    allow_self_edit = "--allow-self-edit" in sys.argv
    if "--offline" in sys.argv:
        ai_config.FORCE_OFFLINE = True
    try:
        import requests  # type: ignore
        ai_config.GROQ_AVAILABLE = bool(ai_config.GROQ_API_KEY and requests)
    except Exception:
        ai_config.GROQ_AVAILABLE = False
    print("Starting agent. Groq available:", ai_config.GROQ_AVAILABLE)
    repl(allow_self_edit=allow_self_edit)


if __name__ == "__main__":
    main()

from __future__ import annotations
import os
import datetime

# Minimal configuration
HISTORY_FILE = os.path.join(os.path.dirname(__file__), ".ai_agent_history.json")
SELF_PATH = os.path.realpath(__file__)
DANGEROUS_SHELL_KEYWORDS = {"rm", "sudo", "mv", "dd", "mkfs", ">:","|", "shred"}

# Centralized Groq configuration
GROQ_AVAILABLE = False
GROQ_API_KEY = os.environ.get("GROQ_API_KEY", "")
GROQ_MODEL = os.environ.get("GROQ_MODEL", "groq-1")
# Default Groq inference URL (can be overridden with GROQ_API_URL)
GROQ_API_URL = os.environ.get("GROQ_API_URL", f"https://api.groq.ai/v1/models/{GROQ_MODEL}/outputs")
GROQ_HOST_IP = os.environ.get("GROQ_HOST_IP", "")
# If set to a truthy value (e.g. '1' or 'true'), do not verify TLS certificates. FOR TESTING ONLY.
GROQ_INSECURE = os.environ.get("GROQ_INSECURE", "").lower() in ("1", "true", "yes")

# If set to True (via CLI --offline) the agent will not attempt network calls to Groq
FORCE_OFFLINE = False

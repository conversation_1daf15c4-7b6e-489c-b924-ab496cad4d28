from __future__ import annotations
import datetime
import json
from typing import List, Dict, Any
import ai_config

def now_ts() -> str:
    # Use timezone-aware UTC datetime to avoid deprecation warning and match previous 'Z' suffix
    return datetime.datetime.now(datetime.timezone.utc).isoformat().replace("+00:00", "Z")

def load_history() -> List[Dict[str, Any]]:
    try:
        with open(ai_config.HISTORY_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return []

def save_history(history: List[Dict[str, Any]]) -> None:
    try:
        with open(ai_config.HISTORY_FILE, "w", encoding="utf-8") as f:
            json.dump(history, f, indent=2)
    except Exception as e:
        print("Failed to save history:", e)

def log(history: List[Dict[str, Any]], role: str, text: str) -> None:
    history.append({"ts": now_ts(), "role": role, "text": text})
    # keep last 200 interactions
    if len(history) > 200:
        del history[0: len(history) - 200]

def confirm(prompt: str, auto_no: bool = False) -> bool:
    if auto_no:
        return False
    resp = input(f"{prompt} Type CONFIRM to proceed: ")
    return resp.strip() == "CONFIRM"

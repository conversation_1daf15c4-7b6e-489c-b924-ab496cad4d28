from __future__ import annotations
import json
from typing import List, Dict, Any
import ai_config

try:
    import requests  # type: ignore
except Exception:
    requests = None  # type: ignore

def llm_think(prompt: str, history: List[Dict[str, Any]]) -> str:
    # Respect force-offline first
    if ai_config.FORCE_OFFLINE:
        return f"[local-mode] OFFLINE: I received: {prompt}\n(Network calls are disabled with --offline.)"

    # Try local LM Studio first if available
    if not ai_config.GROQ_API_KEY and requests and ai_config.LOCAL_LLM_URL:
        try:
            return _call_local_llm(prompt, history)
        except Exception as e:
            return f"[Local LLM error] {e}\n\nFalling back to local mode."

    # Fall back to Groq if available
    if ai_config.GROQ_API_KEY and requests:
        try:
            headers = {"Authorization": f"Bearer {ai_config.GROQ_API_KEY}", "Content-Type": "application/json"}
            payload = {"input": prompt}
            url = ai_config.GROQ_API_URL
            verify = not ai_config.GROQ_INSECURE
            extra_host_header = None
            try:
                from urllib.parse import urlparse, urlunparse
                parsed = urlparse(ai_config.GROQ_API_URL)
                if ai_config.GROQ_HOST_IP:
                    netloc = ai_config.GROQ_HOST_IP
                    if parsed.port:
                        netloc = f"{ai_config.GROQ_HOST_IP}:{parsed.port}"
                    parsed = parsed._replace(netloc=netloc)
                    url = urlunparse(parsed)
                    extra_host_header = parsed.hostname
            except Exception:
                url = ai_config.GROQ_API_URL

            if extra_host_header:
                headers["Host"] = parsed.hostname or "api.groq.ai"

            resp = requests.post(url, headers=headers, json=payload, timeout=20, verify=verify)
            try:
                data = resp.json()
            except Exception:
                return f"[GROQ error] Non-JSON response: {resp.text}"
            if isinstance(data, dict):
                if "output" in data:
                    out = data["output"]
                    if isinstance(out, str):
                        return out
                    if isinstance(out, list) and out:
                        parts = []
                        for item in out:
                            if isinstance(item, str):
                                parts.append(item)
                            elif isinstance(item, dict) and "text" in item:
                                parts.append(str(item["text"]))
                        if parts:
                            return "\n".join(parts)
                if "result" in data:
                    r = data["result"]
                    if isinstance(r, str):
                        return r
                if "text" in data:
                    return str(data["text"])
                if "choices" in data and isinstance(data["choices"], list) and data["choices"]:
                    c0 = data["choices"][0]
                    if isinstance(c0, dict):
                        for k in ("text", "message", "content"):
                            if k in c0:
                                return str(c0[k])
                        if "message" in c0 and isinstance(c0["message"], dict) and "content" in c0["message"]:
                            return str(c0["message"]["content"])
            return json.dumps(data)
        except Exception as e:
            msg = str(e)
            dns_indicators = (
                "Name or service not known",
                "Failed to resolve",
                "Could not resolve host",
                "NameResolutionError",
                "NXDOMAIN",
            )
            if any(ind in msg for ind in dns_indicators):
                return (
                    "[GROQ DNS error] Failed to resolve the Groq API host (api.groq.ai).\n"
                    "Check your DNS/network or set an alternate endpoint via GROQ_API_URL.\n"
                    "Quick checks: 'nslookup api.groq.ai *******' or 'dig @******* api.groq.ai'.\n"
                    "If you're behind a proxy, set HTTPS_PROXY/HTTP_PROXY environment variables."
                )
            return f"[GROQ error] {e}"

    return f"[local-mode] I received: {prompt}\n(Enable LLM via GROQ_API_KEY or LOCAL_LLM_URL and install 'requests' to enable LLM calls.)"


def _call_local_llm(prompt: str, history: List[Dict[str, Any]]) -> str:
    """Call local LM Studio using OpenAI-compatible API"""
    headers = {
        "Authorization": f"Bearer {ai_config.LOCAL_LLM_API_KEY}",
        "Content-Type": "application/json"
    }

    # Build messages from history + current prompt
    messages = []

    # Add recent history as context (last 10 exchanges)
    recent_history = history[-20:] if len(history) > 20 else history
    for entry in recent_history:
        role = entry.get("role", "")
        text = entry.get("text", "")
        if role == "user":
            messages.append({"role": "user", "content": text})
        elif role == "assistant":
            messages.append({"role": "assistant", "content": text})
        # Skip system messages for now to keep it simple

    # Add current prompt
    messages.append({"role": "user", "content": prompt})

    payload = {
        "model": ai_config.LOCAL_LLM_MODEL,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 2048,
        "stream": False
    }

    resp = requests.post(ai_config.LOCAL_LLM_URL, headers=headers, json=payload, timeout=60)
    resp.raise_for_status()

    data = resp.json()

    # Extract response from OpenAI-compatible format
    if "choices" in data and data["choices"]:
        choice = data["choices"][0]
        if "message" in choice and "content" in choice["message"]:
            return choice["message"]["content"]

    # Fallback parsing
    return json.dumps(data)

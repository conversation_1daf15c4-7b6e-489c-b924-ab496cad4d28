from __future__ import annotations
import json
from typing import List, Dict, Any
import ai_config

try:
    import requests  # type: ignore
except Exception:
    requests = None  # type: ignore

def llm_think(prompt: str, history: List[Dict[str, Any]]) -> str:
    # Respect force-offline first
    if ai_config.FORCE_OFFLINE:
        return f"[local-mode] OFFLINE: I received: {prompt}\n(Network calls are disabled with --offline.)"

    if ai_config.GROQ_API_KEY and requests:
        try:
            headers = {"Authorization": f"Bearer {ai_config.GROQ_API_KEY}", "Content-Type": "application/json"}
            payload = {"input": prompt}
            url = ai_config.GROQ_API_URL
            verify = not ai_config.GROQ_INSECURE
            extra_host_header = None
            try:
                from urllib.parse import urlparse, urlunparse
                parsed = urlparse(ai_config.GROQ_API_URL)
                if ai_config.GROQ_HOST_IP:
                    netloc = ai_config.GROQ_HOST_IP
                    if parsed.port:
                        netloc = f"{ai_config.GROQ_HOST_IP}:{parsed.port}"
                    parsed = parsed._replace(netloc=netloc)
                    url = urlunparse(parsed)
                    extra_host_header = parsed.hostname
            except Exception:
                url = ai_config.GROQ_API_URL

            if extra_host_header:
                headers["Host"] = parsed.hostname or "api.groq.ai"

            resp = requests.post(url, headers=headers, json=payload, timeout=20, verify=verify)
            try:
                data = resp.json()
            except Exception:
                return f"[GROQ error] Non-JSON response: {resp.text}"
            if isinstance(data, dict):
                if "output" in data:
                    out = data["output"]
                    if isinstance(out, str):
                        return out
                    if isinstance(out, list) and out:
                        parts = []
                        for item in out:
                            if isinstance(item, str):
                                parts.append(item)
                            elif isinstance(item, dict) and "text" in item:
                                parts.append(str(item["text"]))
                        if parts:
                            return "\n".join(parts)
                if "result" in data:
                    r = data["result"]
                    if isinstance(r, str):
                        return r
                if "text" in data:
                    return str(data["text"])
                if "choices" in data and isinstance(data["choices"], list) and data["choices"]:
                    c0 = data["choices"][0]
                    if isinstance(c0, dict):
                        for k in ("text", "message", "content"):
                            if k in c0:
                                return str(c0[k])
                        if "message" in c0 and isinstance(c0["message"], dict) and "content" in c0["message"]:
                            return str(c0["message"]["content"])
            return json.dumps(data)
        except Exception as e:
            msg = str(e)
            dns_indicators = (
                "Name or service not known",
                "Failed to resolve",
                "Could not resolve host",
                "NameResolutionError",
                "NXDOMAIN",
            )
            if any(ind in msg for ind in dns_indicators):
                return (
                    "[GROQ DNS error] Failed to resolve the Groq API host (api.groq.ai).\n"
                    "Check your DNS/network or set an alternate endpoint via GROQ_API_URL.\n"
                    "Quick checks: 'nslookup api.groq.ai *******' or 'dig @******* api.groq.ai'.\n"
                    "If you're behind a proxy, set HTTPS_PROXY/HTTP_PROXY environment variables."
                )
            return f"[GROQ error] {e}"

    return f"[local-mode] I received: {prompt}\n(Enable GROQ via GROQ_API_KEY and install 'requests' to enable Groq LLM calls.)"

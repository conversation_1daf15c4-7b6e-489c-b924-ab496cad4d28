"""Simple GUI for the AI Terminal Agent

Features:
- Output pane showing assistant/system output
- Input box for Think / shell commands
- Buttons: Think, Run Shell, Show Config, Set Config, Toggle Offline, Clear Output

This GUI uses the existing modules (llm, shell_utils, utils, ai_config) so it shares
the same configuration and history file.
"""
from __future__ import annotations
import threading
import tkinter as tk
from tkinter import scrolledtext, simpledialog, messagebox
import traceback

import ai_config
import llm
import shell_utils
import utils


class AgentGUI:
    def __init__(self, root: tk.Tk):
        self.root = root
        root.title("AI Agent GUI")

        # Output area
        self.output = scrolledtext.ScrolledText(root, wrap=tk.WORD, state=tk.NORMAL, height=20)
        self.output.pack(fill=tk.BOTH, expand=True, padx=8, pady=6)

        # Input frame
        frm = tk.Frame(root)
        frm.pack(fill=tk.X, padx=8, pady=4)

        tk.Label(frm, text="Input:").pack(side=tk.LEFT)
        self.input = tk.Entry(frm)
        self.input.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=6)

        btn_frame = tk.Frame(root)
        btn_frame.pack(fill=tk.X, padx=8, pady=4)

        tk.Button(btn_frame, text="Think", command=self.on_think).pack(side=tk.LEFT, padx=4)
        tk.Button(btn_frame, text="Run Shell", command=self.on_run_shell).pack(side=tk.LEFT, padx=4)
        tk.Button(btn_frame, text="Show Config", command=self.on_show_config).pack(side=tk.LEFT, padx=4)
        tk.Button(btn_frame, text="Set Config", command=self.on_set_config).pack(side=tk.LEFT, padx=4)
        tk.Button(btn_frame, text="Toggle Offline", command=self.on_toggle_offline).pack(side=tk.LEFT, padx=4)
        tk.Button(btn_frame, text="Clear Output", command=lambda: self.output.delete(1.0, tk.END)).pack(side=tk.RIGHT, padx=4)

        # Status bar
        self.status = tk.Label(root, text=self.status_text(), anchor=tk.W)
        self.status.pack(fill=tk.X, padx=8, pady=(0,6))

        # bind enter in input to Think
        self.input.bind("<Return>", lambda e: self.on_think())

    def status_text(self) -> str:
        return f"Groq available: {ai_config.GROQ_API_KEY and 'set' or 'unset'} | Offline: {ai_config.FORCE_OFFLINE}"

    def append_output(self, text: str) -> None:
        self.output.configure(state=tk.NORMAL)
        self.output.insert(tk.END, text + "\n")
        self.output.see(tk.END)
        self.output.configure(state=tk.DISABLED)

    def run_in_thread(self, target, *args, **kwargs):
        def runner():
            try:
                target(*args, **kwargs)
            except Exception:
                self.append_output("[GUI error] " + traceback.format_exc())
        t = threading.Thread(target=runner, daemon=True)
        t.start()

    def on_think(self):
        prompt = self.input.get().strip()
        if not prompt:
            messagebox.showinfo("Input needed", "Please enter an instruction to Think.")
            return
        self.append_output(f">>> THINK: {prompt}")
        self.run_in_thread(self._do_think, prompt)

    def _do_think(self, prompt: str):
        out = llm.llm_think(prompt, utils.load_history())
        utils.log(utils.load_history(), "assistant", out)
        utils.save_history(utils.load_history())
        self.append_output(out)
        self.status.config(text=self.status_text())

    def on_run_shell(self):
        cmd = self.input.get().strip()
        if not cmd:
            messagebox.showinfo("Input needed", "Please enter a shell command to run (or prefix with ! in the REPL).")
            return
        if shell_utils.is_dangerous_shell(cmd):
            if not utils.confirm("Dangerous command detected. To proceed, type CONFIRM:"):
                self.append_output("Shell command cancelled by user.")
                return
        self.append_output(f">>> SHELL: {cmd}")
        self.run_in_thread(self._do_shell, cmd)

    def _do_shell(self, cmd: str):
        rc = shell_utils.run_shell(cmd)
        utils.log(utils.load_history(), "system", f"shell return {rc}")
        utils.save_history(utils.load_history())
        self.append_output(f"[shell return] {rc}")

    def on_show_config(self):
        masked = (ai_config.GROQ_API_KEY[:4] + "..." + ai_config.GROQ_API_KEY[-4:]) if ai_config.GROQ_API_KEY else "<unset>"
        cfg = (
            f"GROQ_API_KEY: {masked}\n"
            f"GROQ_MODEL: {ai_config.GROQ_MODEL}\n"
            f"GROQ_API_URL: {ai_config.GROQ_API_URL}\n"
            f"GROQ_HOST_IP: {ai_config.GROQ_HOST_IP or '<unset>'}\n"
            f"GROQ_INSECURE: {ai_config.GROQ_INSECURE}\n"
            f"FORCE_OFFLINE: {ai_config.FORCE_OFFLINE}\n"
        )
        messagebox.showinfo("Config", cfg)

    def on_set_config(self):
        # Ask which key to set
        key = simpledialog.askstring("Set Config", "Enter key (GROQ_API_KEY, GROQ_HOST_IP, GROQ_API_URL, GROQ_MODEL, GROQ_INSECURE):")
        if not key:
            return
        key = key.strip()
        val = simpledialog.askstring("Set Config", f"Enter value for {key}:")
        if val is None:
            return
        if key == "GROQ_INSECURE":
            ai_config.GROQ_INSECURE = val.lower() in ("1", "true", "yes")
        else:
            setattr(ai_config, key, val)
        # update availability
        try:
            import requests as _req
            ai_config.GROQ_AVAILABLE = bool(ai_config.GROQ_API_KEY and _req)
        except Exception:
            ai_config.GROQ_AVAILABLE = False
        self.status.config(text=self.status_text())
        messagebox.showinfo("Set", f"Set {key}.")

    def on_toggle_offline(self):
        ai_config.FORCE_OFFLINE = not ai_config.FORCE_OFFLINE
        self.status.config(text=self.status_text())
        self.append_output(f"Offline mode set to: {ai_config.FORCE_OFFLINE}")


def main():
    root = tk.Tk()
    app = AgentGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

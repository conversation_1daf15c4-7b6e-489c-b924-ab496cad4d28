from __future__ import annotations
import os
import tempfile
import subprocess
import shlex
from typing import Optional
import utils
import ai_config

def tiny_editor(content: str) -> Optional[str]:
    lines = content.splitlines()
    while True:
        print("\n--- Tiny Editor ---")
        n = len(lines)
        head = "\n".join(f"{i+1:3d}: {l}" for i, l in enumerate(lines[:50]))
        print(head)
        if n > 50:
            print(f"... (showing first 50 of {n} lines)")
        print("\nCommands: a (append), i <n> (insert after n), d <n> (delete), r <n> (replace), s (save), q (quit discard)")
        cmd = input("edit> ").strip()
        if not cmd:
            continue
        parts = cmd.split(maxsplit=1)
        op = parts[0]
        arg = parts[1] if len(parts) > 1 else ""
        try:
            if op == "a":
                print("Enter lines. End with a single '.' on its own line.")
                buf = []
                while True:
                    l = input()
                    if l == ".":
                        break
                    buf.append(l)
                lines.extend(buf)
            elif op == "i":
                idx = int(arg) if arg else len(lines)
                print("Insert lines. End with '.'")
                buf = []
                while True:
                    l = input()
                    if l == ".":
                        break
                    buf.append(l)
                lines[idx:idx] = buf
            elif op == "d":
                idx = int(arg) - 1
                if 0 <= idx < len(lines):
                    del lines[idx]
            elif op == "r":
                idx = int(arg) - 1
                if 0 <= idx < len(lines):
                    print("New content for line (single).")
                    lines[idx] = input()
            elif op == "s":
                return "\n".join(lines) + ("\n" if lines and not lines[-1].endswith("\n") else "")
            elif op == "q":
                return None
            else:
                print("Unknown edit command.")
        except Exception as e:
            print("Edit error:", e)

def edit_file(path: str) -> bool:
    path = os.path.abspath(path)
    if os.path.isdir(path):
        print("Cannot edit a directory.")
        return False
    try:
        orig = ""
        if os.path.exists(path):
            with open(path, "r", encoding="utf-8", errors="ignore") as f:
                orig = f.read()
        editor = os.environ.get("EDITOR")
        if editor:
            with tempfile.NamedTemporaryFile("w+", delete=False, suffix=".tmp", encoding="utf-8") as tf:
                tf.write(orig)
                tf.flush()
                tfname = tf.name
            try:
                subprocess.call(shlex.split(f"{editor} {shlex.quote(tfname)}"))
                with open(tfname, "r", encoding="utf-8") as f:
                    new = f.read()
            finally:
                try:
                    os.unlink(tfname)
                except Exception:
                    pass
        else:
            new = tiny_editor(orig)
            if new is None:
                print("Edit aborted.")
                return False
        if new == orig:
            print("No changes made.")
            return False
        if len(new) > 200_000:
            print(f"File appears large ({len(new)} bytes).")
            if not input(f"Write a large file? Type CONFIRM to proceed: ") == "CONFIRM":
                print("Aborted.")
                return False
        with open(path, "w", encoding="utf-8") as f:
            f.write(new)
        print(f"Wrote {len(new)} bytes to {path}")
        return True
    except Exception as e:
        print("Edit failed:", e)
        return False

from __future__ import annotations
import shlex
import subprocess
from typing import List
import ai_config

def is_dangerous_shell(cmd: str) -> bool:
    tokens = set(shlex.split(cmd))
    for k in ai_config.DANGEROUS_SHELL_KEYWORDS:
        if k in cmd or k in tokens:
            return True
    return False

def run_shell(cmd: str) -> int:
    try:
        parts = shlex.split(cmd)
        proc = subprocess.Popen(parts, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=1, universal_newlines=True)
        assert proc.stdout is not None
        for line in proc.stdout:
            print(line, end="")
        proc.wait()
        return proc.returncode or 0
    except FileNotFoundError:
        print("Command not found. Trying via shell fallback.")
        return subprocess.call(cmd, shell=True)
    except Exception as e:
        print("Error running command:", e)
        return 1

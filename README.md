AI Terminal Agent

This is a small interactive terminal agent. By default it will run in local (non-LLM) mode.

Quick start

1. Install minimal dependency for Groq:

```sh
python3 -m pip install --user -r requirements.txt
```

2. Set your Groq API key in the environment (example):

```sh
export GROQ_API_KEY="your-groq-key-here"
```

Optionally set the model or API URL:

```sh
export GROQ_MODEL="groq-1"
# or custom API URL
export GROQ_API_URL="https://api.groq.ai/v1/models/groq-1/outputs"
```

3. Run the agent:

```sh
python3 ai_agent.py
```

Using the agent

- Type `help` in the agent to see commands.
- Use `think <instruction>` to send a prompt to Groq when `GROQ_API_KEY` is set.

Notes

- This project prefers Groq if `GROQ_API_KEY` is present and `requests` is installed.
- The script also supports OpenAI if `OPENAI_API_KEY` and `openai` are present, but you can ignore that if you only want Groq.

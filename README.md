AI Terminal Agent

This is a small interactive terminal agent that supports both cloud-based LLMs (Groq) and local models (LM Studio). By default it will run in local (non-LLM) mode.

Quick start

1. Install minimal dependency:

```sh
python3 -m pip install --user -r requirements.txt
```

2. Configure your LLM (choose one):

**Option A: Local LM Studio (Recommended)**
```sh
export LOCAL_LLM_URL="http://127.0.0.1:1234/v1/chat/completions"
export LOCAL_LLM_MODEL="openai/gpt-oss-20b"
```

**Option B: Groq Cloud API**
```sh
export GROQ_API_KEY="your-groq-key-here"
export GROQ_MODEL="groq-1"  # optional
```

3. Run the agent:

```sh
python3 ai_agent.py
```

Using the agent

- Type `help` in the agent to see commands.
- Use `think <instruction>` to send a prompt to your configured LLM.
- Use `config` to view current LLM configuration.
- Use `set LOCAL_LLM_URL <url>` to change settings at runtime.

LM Studio Setup

1. Download and install LM Studio
2. Load your preferred model (e.g., openai/gpt-oss-20b)
3. Start the local server (default: http://127.0.0.1:1234)
4. The agent will automatically detect and use the local model

Notes

- Local models via LM Studio are preferred when `LOCAL_LLM_URL` is set
- Falls back to Groq if `GROQ_API_KEY` is present and no local URL is configured
- Requires `requests` package for LLM functionality
